# PostgreSQL Configuration for MCP Server
# Copy this file to .env and update with your database credentials

# PostgreSQL Host (default: localhost)
PG_HOST=localhost

# PostgreSQL Port (default: 5432)
PG_PORT=5432

# PostgreSQL Username (required)
PG_USER=your_username

# PostgreSQL Password (required)
PG_PASSWORD=your_password

# PostgreSQL Database Name (required)
PG_DATABASE=your_database_name

# Example configuration for local development:
# PG_HOST=localhost
# PG_PORT=5432
# PG_USER=postgres
# PG_PASSWORD=mypassword
# PG_DATABASE=mcp_demo

# Example configuration for cloud database:
# PG_HOST=your-cloud-db-host.com
# PG_PORT=5432
# PG_USER=your_cloud_user
# PG_PASSWORD=your_cloud_password
# PG_DATABASE=your_cloud_database
