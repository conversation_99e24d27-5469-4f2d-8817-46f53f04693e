# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST

# Virtual environments
.env
.venv/
env/
venv/
ENV/
env.bak/
venv.bak/

# IDEs
.vscode/
*.swp
*.swo
*~

# OS
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Testing
.coverage
.pytest_cache/
.tox/
.nox/
htmlcov/
.cache
nosetests.xml
coverage.xml
*.cover
.hypothesis/

# Logs
*.log
logs/
clog.md

# Database
*.db
*.sqlite3

# Temporary files
*.tmp
*.temp
.tmp/

# Project specific
.env.local
.env.production
.env.staging

# Development files 
examples/
scripts/
tests/
demo.py
Makefile
.github/